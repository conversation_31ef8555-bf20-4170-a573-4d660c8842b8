<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('equipment_maintenances', function (Blueprint $table) {
            $table->id();
            $table->string('maintenance_number')->unique();
            $table->string('equipment_name');
            $table->string('equipment_code')->nullable();
            $table->string('equipment_type')->nullable(); // Machine, Tool, Vehicle, etc.
            $table->text('equipment_description')->nullable();
            $table->enum('maintenance_type', ['preventive', 'corrective', 'emergency', 'routine']);
            $table->enum('priority', ['low', 'normal', 'high', 'critical'])->default('normal');
            $table->enum('status', ['scheduled', 'in_progress', 'completed', 'cancelled', 'on_hold'])->default('scheduled');
            $table->text('description');
            $table->text('problem_description')->nullable();
            $table->text('solution_description')->nullable();
            $table->datetime('scheduled_start');
            $table->datetime('scheduled_end');
            $table->datetime('actual_start')->nullable();
            $table->datetime('actual_end')->nullable();
            $table->integer('estimated_duration_hours')->nullable();
            $table->integer('actual_duration_hours')->nullable();
            $table->decimal('estimated_cost', 12, 2)->nullable();
            $table->decimal('actual_cost', 12, 2)->nullable();
            $table->foreignId('assigned_to')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('supervisor_id')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('requested_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('approved_at')->nullable();
            $table->text('notes')->nullable();
            $table->json('spare_parts_used')->nullable(); // List of spare parts used
            $table->json('attachments')->nullable(); // Photos, documents
            $table->string('vendor_name')->nullable(); // External maintenance vendor
            $table->string('vendor_contact')->nullable();
            $table->date('next_maintenance_date')->nullable();
            $table->integer('maintenance_interval_days')->nullable();
            $table->foreignId('warehouse_id')->nullable()->constrained()->onDelete('set null');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['equipment_name', 'maintenance_type']);
            $table->index(['status', 'scheduled_start']);
            $table->index(['assigned_to', 'status']);
            $table->index(['next_maintenance_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('equipment_maintenances');
    }
};
