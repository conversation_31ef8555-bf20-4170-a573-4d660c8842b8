<?php

namespace App\Filament\Manufacturing\Resources\ProductionOrderResource\Pages;

use App\Filament\Manufacturing\Resources\ProductionOrderResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditProductionOrder extends EditRecord
{
    protected static string $resource = ProductionOrderResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', ['record' => $this->getRecord()]);
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Auto-calculate total costs
        $data['total_planned_cost'] = ($data['planned_material_cost'] ?? 0) + 
                                     ($data['planned_labor_cost'] ?? 0) + 
                                     ($data['planned_overhead_cost'] ?? 0);
        
        $data['total_actual_cost'] = ($data['actual_material_cost'] ?? 0) + 
                                    ($data['actual_labor_cost'] ?? 0) + 
                                    ($data['actual_overhead_cost'] ?? 0);

        return $data;
    }
}
