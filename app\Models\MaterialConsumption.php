<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MaterialConsumption extends Model
{
    use HasFactory;

    protected $fillable = [
        'production_order_id',
        'production_stage_id',
        'product_id',
        'warehouse_id',
        'quantity_consumed',
        'unit',
        'unit_cost',
        'total_cost',
        'consumption_date',
        'consumed_by',
        'consumption_type',
        'reason',
        'batch_number',
        'lot_number',
        'notes',
    ];

    protected $casts = [
        'quantity_consumed' => 'decimal:4',
        'unit_cost' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'consumption_date' => 'datetime',
    ];

    // Relationships
    public function productionOrder(): BelongsTo
    {
        return $this->belongsTo(ProductionOrder::class);
    }

    public function productionStage(): BelongsTo
    {
        return $this->belongsTo(ProductionStage::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function warehouse(): BelongsTo
    {
        return $this->belongsTo(Warehouse::class);
    }

    public function consumer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'consumed_by');
    }

    // Scopes
    public function scopeNormalConsumption($query)
    {
        return $query->where('consumption_type', 'normal');
    }

    public function scopeWaste($query)
    {
        return $query->where('consumption_type', 'waste');
    }

    public function scopeRework($query)
    {
        return $query->where('consumption_type', 'rework');
    }

    public function scopeForProduct($query, $productId)
    {
        return $query->where('product_id', $productId);
    }

    public function scopeForPeriod($query, $startDate, $endDate)
    {
        return $query->whereBetween('consumption_date', [$startDate, $endDate]);
    }

    // Methods
    public static function recordConsumption(array $data): self
    {
        $consumption = static::create($data);
        
        // Update inventory stock
        $consumption->updateInventoryStock();
        
        // Update production order material consumption
        $consumption->updateProductionOrderMaterial();
        
        return $consumption;
    }

    protected function updateInventoryStock(): void
    {
        $inventoryStock = InventoryStock::where([
            'product_id' => $this->product_id,
            'warehouse_id' => $this->warehouse_id,
        ])->first();

        if ($inventoryStock) {
            $inventoryStock->decrement('quantity', $this->quantity_consumed);
        }
    }

    protected function updateProductionOrderMaterial(): void
    {
        $orderMaterial = ProductionOrderMaterial::where([
            'production_order_id' => $this->production_order_id,
            'product_id' => $this->product_id,
        ])->first();

        if ($orderMaterial) {
            $orderMaterial->increment('consumed_quantity', $this->quantity_consumed);
        }
    }

    public function getVarianceFromPlanned(): float
    {
        $orderMaterial = ProductionOrderMaterial::where([
            'production_order_id' => $this->production_order_id,
            'product_id' => $this->product_id,
        ])->first();

        if (!$orderMaterial) {
            return 0;
        }

        return $this->quantity_consumed - $orderMaterial->required_quantity;
    }

    protected static function boot()
    {
        parent::boot();

        static::saving(function ($consumption) {
            // Auto-calculate total cost
            $consumption->total_cost = $consumption->quantity_consumed * $consumption->unit_cost;
        });
    }
}
