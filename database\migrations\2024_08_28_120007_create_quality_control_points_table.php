<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('quality_control_points', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->enum('type', ['incoming', 'in_process', 'final', 'outgoing'])->default('in_process');
            $table->foreignId('product_id')->nullable()->constrained('products'); // For product-specific QC
            $table->boolean('is_mandatory')->default(true);
            $table->integer('sequence')->default(0);
            $table->json('test_parameters')->nullable(); // Flexible test configuration
            $table->decimal('min_acceptable_value', 10, 4)->nullable();
            $table->decimal('max_acceptable_value', 10, 4)->nullable();
            $table->string('unit_of_measure', 20)->nullable();
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->text('instructions')->nullable();
            $table->foreignId('created_by')->constrained('users');
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['type', 'status']);
            $table->index(['product_id', 'status']);
            $table->index(['sequence', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('quality_control_points');
    }
};
