<?php

namespace App\Providers\Filament;

use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Navigation\NavigationGroup;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Support\Enums\MaxWidth;
use Filament\Widgets;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;

class ManufacturingPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->id('manufacturing')
            ->path('manufacturing')
            ->login()
            ->brandName('Manufacturing Panel')
            ->brandLogo(asset('images/logo.png'))
            ->favicon(asset('images/favicon.ico'))
            ->colors([
                'primary' => Color::Orange,
                'gray' => Color::Slate,
            ])
            ->discoverResources(in: app_path('Filament/Manufacturing/Resources'), for: 'App\\Filament\\Manufacturing\\Resources')
            ->discoverPages(in: app_path('Filament/Manufacturing/Pages'), for: 'App\\Filament\\Manufacturing\\Pages')
            ->pages([
                Pages\Dashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Manufacturing/Widgets'), for: 'App\\Filament\\Manufacturing\\Widgets')
            ->widgets([
                Widgets\AccountWidget::class,
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ])
            ->navigationGroups([
                NavigationGroup::make('Data Master')
                    ->icon('heroicon-o-cube')
                    ->collapsible(),
                NavigationGroup::make('Perencanaan Produksi')
                    ->icon('heroicon-o-calendar-days')
                    ->collapsible(),
                NavigationGroup::make('Operasi Produksi')
                    ->icon('heroicon-o-cog-6-tooth')
                    ->collapsible(),
                NavigationGroup::make('Maintenance & Repair')
                    ->icon('heroicon-o-wrench-screwdriver')
                    ->collapsible(),
                NavigationGroup::make('Kontrol Kualitas')
                    ->icon('heroicon-o-shield-check')
                    ->collapsible(),
                NavigationGroup::make('Laporan & Analitik')
                    ->icon('heroicon-o-chart-bar')
                    ->collapsible(),
            ])
            ->sidebarCollapsibleOnDesktop()
            ->maxContentWidth(MaxWidth::Full);
    }
}
