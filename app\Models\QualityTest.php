<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class QualityTest extends Model
{
    use HasFactory;

    protected $fillable = [
        'quality_inspection_id',
        'test_name',
        'test_description',
        'test_type',
        'measured_value',
        'target_value',
        'min_acceptable',
        'max_acceptable',
        'unit_of_measure',
        'result',
        'observations',
        'test_data',
        'equipment_used',
        'tested_by',
        'test_date',
    ];

    protected $casts = [
        'measured_value' => 'decimal:4',
        'target_value' => 'decimal:4',
        'min_acceptable' => 'decimal:4',
        'max_acceptable' => 'decimal:4',
        'test_data' => 'array',
        'test_date' => 'datetime',
    ];

    // Relationships
    public function qualityInspection(): BelongsTo
    {
        return $this->belongsTo(QualityInspection::class);
    }

    public function tester(): BelongsTo
    {
        return $this->belongsTo(User::class, 'tested_by');
    }

    // Scopes
    public function scopePassed($query)
    {
        return $query->where('result', 'pass');
    }

    public function scopeFailed($query)
    {
        return $query->where('result', 'fail');
    }

    public function scopeWarning($query)
    {
        return $query->where('result', 'warning');
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('test_type', $type);
    }

    // Accessors
    public function getDeviationAttribute(): ?float
    {
        if ($this->measured_value === null || $this->target_value === null) {
            return null;
        }
        
        return $this->measured_value - $this->target_value;
    }

    public function getDeviationPercentageAttribute(): ?float
    {
        if ($this->target_value === null || $this->target_value == 0) {
            return null;
        }
        
        $deviation = $this->getDeviationAttribute();
        return $deviation ? ($deviation / $this->target_value) * 100 : 0;
    }

    public function getIsWithinToleranceAttribute(): bool
    {
        if ($this->measured_value === null) {
            return false;
        }
        
        if ($this->min_acceptable !== null && $this->measured_value < $this->min_acceptable) {
            return false;
        }
        
        if ($this->max_acceptable !== null && $this->measured_value > $this->max_acceptable) {
            return false;
        }
        
        return true;
    }

    // Methods
    public function evaluateResult(): void
    {
        if ($this->measured_value === null) {
            $this->update(['result' => 'fail']);
            return;
        }

        if (!$this->getIsWithinToleranceAttribute()) {
            $this->update(['result' => 'fail']);
            return;
        }

        // Check if it's close to limits (warning zone)
        if ($this->isInWarningZone()) {
            $this->update(['result' => 'warning']);
            return;
        }

        $this->update(['result' => 'pass']);
    }

    protected function isInWarningZone(): bool
    {
        if ($this->measured_value === null) {
            return false;
        }

        $warningThreshold = 0.1; // 10% of the range

        if ($this->min_acceptable !== null && $this->max_acceptable !== null) {
            $range = $this->max_acceptable - $this->min_acceptable;
            $warningMargin = $range * $warningThreshold;
            
            return ($this->measured_value <= $this->min_acceptable + $warningMargin) ||
                   ($this->measured_value >= $this->max_acceptable - $warningMargin);
        }

        return false;
    }

    protected static function boot()
    {
        parent::boot();

        static::saving(function ($test) {
            $test->evaluateResult();
        });

        static::saved(function ($test) {
            // Update the overall inspection result
            $test->qualityInspection->calculateOverallResult();
        });
    }
}
