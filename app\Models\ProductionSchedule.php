<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProductionSchedule extends Model
{
    use HasFactory;

    protected $fillable = [
        'production_plan_id',
        'production_order_id',
        'scheduled_date',
        'scheduled_start_time',
        'scheduled_end_time',
        'scheduled_duration_minutes',
        'scheduled_quantity',
        'assigned_operator',
        'assigned_supervisor',
        'shift',
        'status',
        'actual_start_time',
        'actual_end_time',
        'actual_quantity',
        'notes',
        'delay_reason',
    ];

    protected $casts = [
        'scheduled_date' => 'date',
        'scheduled_start_time' => 'datetime',
        'scheduled_end_time' => 'datetime',
        'actual_start_time' => 'datetime',
        'actual_end_time' => 'datetime',
        'scheduled_quantity' => 'decimal:2',
        'actual_quantity' => 'decimal:2',
    ];

    // Relationships
    public function productionPlan(): BelongsTo
    {
        return $this->belongsTo(ProductionPlan::class);
    }

    public function productionOrder(): BelongsTo
    {
        return $this->belongsTo(ProductionOrder::class);
    }

    public function operator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_operator');
    }

    public function supervisor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_supervisor');
    }

    // Scopes
    public function scopeScheduled($query)
    {
        return $query->where('status', 'scheduled');
    }

    public function scopeInProgress($query)
    {
        return $query->where('status', 'in_progress');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeForDate($query, $date)
    {
        return $query->whereDate('scheduled_date', $date);
    }

    public function scopeForShift($query, string $shift)
    {
        return $query->where('shift', $shift);
    }

    public function scopeForOperator($query, $operatorId)
    {
        return $query->where('assigned_operator', $operatorId);
    }

    // Accessors
    public function getProgressPercentageAttribute(): float
    {
        if ($this->scheduled_quantity == 0) {
            return 0;
        }
        
        return ($this->actual_quantity / $this->scheduled_quantity) * 100;
    }

    public function getScheduleVarianceAttribute(): int
    {
        if (!$this->actual_end_time || !$this->scheduled_end_time) {
            return 0;
        }
        
        return $this->actual_end_time->diffInMinutes($this->scheduled_end_time, false);
    }

    public function getQuantityVarianceAttribute(): float
    {
        return $this->actual_quantity - $this->scheduled_quantity;
    }

    public function getIsDelayedAttribute(): bool
    {
        if ($this->status === 'completed') {
            return $this->actual_end_time > $this->scheduled_end_time;
        }
        
        if ($this->status === 'in_progress') {
            return now() > $this->scheduled_end_time;
        }
        
        return false;
    }

    // Methods
    public function start(): void
    {
        $this->update([
            'status' => 'in_progress',
            'actual_start_time' => now(),
        ]);
    }

    public function complete(float $actualQuantity, string $notes = null): void
    {
        $this->update([
            'status' => 'completed',
            'actual_end_time' => now(),
            'actual_quantity' => $actualQuantity,
            'notes' => $notes,
        ]);
    }

    public function reschedule(array $newScheduleData): void
    {
        $this->update(array_merge($newScheduleData, [
            'status' => 'rescheduled',
        ]));
    }

    public function cancel(string $reason): void
    {
        $this->update([
            'status' => 'cancelled',
            'delay_reason' => $reason,
        ]);
    }

    public function canStart(): bool
    {
        return $this->status === 'confirmed' && 
               now() >= $this->scheduled_start_time->subMinutes(15); // Allow 15 min early start
    }

    public function isOverdue(): bool
    {
        return $this->status !== 'completed' && 
               now() > $this->scheduled_end_time;
    }

    public static function getAvailableTimeSlots($date, $shift, $excludeScheduleId = null)
    {
        $query = static::forDate($date)->forShift($shift);
        
        if ($excludeScheduleId) {
            $query->where('id', '!=', $excludeScheduleId);
        }
        
        $existingSchedules = $query->get();
        
        // Define shift time ranges
        $shiftTimes = [
            'morning' => ['08:00', '16:00'],
            'afternoon' => ['16:00', '00:00'],
            'night' => ['00:00', '08:00'],
        ];
        
        // Logic to find available slots would go here
        // This is a simplified version
        return $shiftTimes[$shift] ?? [];
    }
}
