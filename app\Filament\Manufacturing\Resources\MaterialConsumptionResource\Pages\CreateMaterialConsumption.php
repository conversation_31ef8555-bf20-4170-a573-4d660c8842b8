<?php

namespace App\Filament\Manufacturing\Resources\MaterialConsumptionResource\Pages;

use App\Filament\Manufacturing\Resources\MaterialConsumptionResource;
use Filament\Resources\Pages\CreateRecord;

class CreateMaterialConsumption extends CreateRecord
{
    protected static string $resource = MaterialConsumptionResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function afterCreate(): void
    {
        // Record the consumption using the model method
        $this->record->updateInventoryStock();
        $this->record->updateProductionOrderMaterial();
    }
}
