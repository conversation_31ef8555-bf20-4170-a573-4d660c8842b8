<?php

namespace App\Filament\Manufacturing\Widgets;

use App\Models\ProductionOrder;
use Filament\Widgets\ChartWidget;

class CostAnalysisWidget extends ChartWidget
{
    protected static ?string $heading = 'Analisis Biaya (Rencana vs Aktual)';

    protected static ?int $sort = 3;

    protected function getData(): array
    {
        $completedOrders = ProductionOrder::where('status', 'completed')
            ->whereNotNull('actual_end_date')
            ->whereDate('actual_end_date', '>=', now()->subDays(30))
            ->get();

        if ($completedOrders->isEmpty()) {
            return [
                'datasets' => [],
                'labels' => [],
            ];
        }

        $plannedMaterial = $completedOrders->sum('planned_material_cost');
        $actualMaterial = $completedOrders->sum('actual_material_cost');
        $plannedLabor = $completedOrders->sum('planned_labor_cost');
        $actualLabor = $completedOrders->sum('actual_labor_cost');
        $plannedOverhead = $completedOrders->sum('planned_overhead_cost');
        $actualOverhead = $completedOrders->sum('actual_overhead_cost');

        return [
            'datasets' => [
                [
                    'label' => 'Planned Cost',
                    'data' => [
                        $plannedMaterial / 1000000, // Convert to millions
                        $plannedLabor / 1000000,
                        $plannedOverhead / 1000000,
                    ],
                    'backgroundColor' => 'rgba(156, 163, 175, 0.8)',
                    'borderColor' => 'rgb(156, 163, 175)',
                    'borderWidth' => 1,
                ],
                [
                    'label' => 'Actual Cost',
                    'data' => [
                        $actualMaterial / 1000000,
                        $actualLabor / 1000000,
                        $actualOverhead / 1000000,
                    ],
                    'backgroundColor' => 'rgba(239, 68, 68, 0.8)',
                    'borderColor' => 'rgb(239, 68, 68)',
                    'borderWidth' => 1,
                ],
            ],
            'labels' => ['Material', 'Labor', 'Overhead'],
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
                'tooltip' => [
                    'callbacks' => [
                        'label' => 'function(context) { return context.dataset.label + ": Rp " + (context.parsed.y * 1000000).toLocaleString() + ""; }',
                    ],
                ],
            ],
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'callback' => 'function(value) { return "Rp " + value + "M"; }',
                    ],
                ],
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
        ];
    }
}
