<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class QualityInspection extends Model
{
    use HasFactory;

    protected $fillable = [
        'inspection_number',
        'production_order_id',
        'production_stage_id',
        'quality_control_point_id',
        'quantity_inspected',
        'quantity_passed',
        'quantity_failed',
        'quantity_rework',
        'overall_result',
        'inspection_date',
        'inspector_id',
        'notes',
        'corrective_actions',
        'inspection_data',
        'requires_rework',
        'is_final_inspection',
    ];

    protected $casts = [
        'quantity_inspected' => 'decimal:2',
        'quantity_passed' => 'decimal:2',
        'quantity_failed' => 'decimal:2',
        'quantity_rework' => 'decimal:2',
        'inspection_date' => 'datetime',
        'inspection_data' => 'array',
        'requires_rework' => 'boolean',
        'is_final_inspection' => 'boolean',
    ];

    // Relationships
    public function productionOrder(): BelongsTo
    {
        return $this->belongsTo(ProductionOrder::class);
    }

    public function productionStage(): BelongsTo
    {
        return $this->belongsTo(ProductionStage::class);
    }

    public function qualityControlPoint(): BelongsTo
    {
        return $this->belongsTo(QualityControlPoint::class);
    }

    public function inspector(): BelongsTo
    {
        return $this->belongsTo(User::class, 'inspector_id');
    }

    public function qualityTests(): HasMany
    {
        return $this->hasMany(QualityTest::class);
    }

    // Scopes
    public function scopePassed($query)
    {
        return $query->where('overall_result', 'passed');
    }

    public function scopeFailed($query)
    {
        return $query->where('overall_result', 'failed');
    }

    public function scopePending($query)
    {
        return $query->where('overall_result', 'pending');
    }

    public function scopeRequiresRework($query)
    {
        return $query->where('requires_rework', true);
    }

    public function scopeFinalInspections($query)
    {
        return $query->where('is_final_inspection', true);
    }

    // Accessors
    public function getPassRateAttribute(): float
    {
        if ($this->quantity_inspected == 0) {
            return 0;
        }
        
        return ($this->quantity_passed / $this->quantity_inspected) * 100;
    }

    public function getFailRateAttribute(): float
    {
        if ($this->quantity_inspected == 0) {
            return 0;
        }
        
        return ($this->quantity_failed / $this->quantity_inspected) * 100;
    }

    public function getReworkRateAttribute(): float
    {
        if ($this->quantity_inspected == 0) {
            return 0;
        }
        
        return ($this->quantity_rework / $this->quantity_inspected) * 100;
    }

    // Methods
    public function addTest(array $testData): QualityTest
    {
        return $this->qualityTests()->create(array_merge($testData, [
            'tested_by' => auth()->id(),
            'test_date' => now(),
        ]));
    }

    public function calculateOverallResult(): void
    {
        $tests = $this->qualityTests;
        
        if ($tests->isEmpty()) {
            $this->update(['overall_result' => 'pending']);
            return;
        }

        $failedTests = $tests->where('result', 'fail');
        $warningTests = $tests->where('result', 'warning');

        if ($failedTests->isNotEmpty()) {
            $this->update([
                'overall_result' => 'failed',
                'requires_rework' => true,
            ]);
        } elseif ($warningTests->isNotEmpty()) {
            $this->update(['overall_result' => 'conditional_pass']);
        } else {
            $this->update(['overall_result' => 'passed']);
        }

        $this->updateQuantities();
    }

    protected function updateQuantities(): void
    {
        switch ($this->overall_result) {
            case 'passed':
                $this->update([
                    'quantity_passed' => $this->quantity_inspected,
                    'quantity_failed' => 0,
                    'quantity_rework' => 0,
                ]);
                break;
                
            case 'failed':
                if ($this->requires_rework) {
                    $this->update([
                        'quantity_passed' => 0,
                        'quantity_failed' => 0,
                        'quantity_rework' => $this->quantity_inspected,
                    ]);
                } else {
                    $this->update([
                        'quantity_passed' => 0,
                        'quantity_failed' => $this->quantity_inspected,
                        'quantity_rework' => 0,
                    ]);
                }
                break;
                
            case 'conditional_pass':
                // For conditional pass, assume most quantity passes with some rework
                $reworkQuantity = $this->quantity_inspected * 0.1; // 10% for rework
                $this->update([
                    'quantity_passed' => $this->quantity_inspected - $reworkQuantity,
                    'quantity_failed' => 0,
                    'quantity_rework' => $reworkQuantity,
                ]);
                break;
        }
    }

    public function generateInspectionNumber(): string
    {
        $prefix = 'QI';
        $date = now()->format('Ymd');
        $sequence = static::whereDate('created_at', today())->count() + 1;
        
        return sprintf('%s%s%04d', $prefix, $date, $sequence);
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($inspection) {
            if (empty($inspection->inspection_number)) {
                $inspection->inspection_number = $inspection->generateInspectionNumber();
            }
        });
    }
}
