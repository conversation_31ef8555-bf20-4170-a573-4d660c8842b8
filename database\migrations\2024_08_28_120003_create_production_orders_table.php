<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('production_orders', function (Blueprint $table) {
            $table->id();
            $table->string('po_number')->unique();
            $table->foreignId('production_plan_id')->nullable()->constrained('production_plans')->onDelete('set null');
            $table->foreignId('bom_id')->constrained('boms');
            $table->foreignId('product_id')->constrained('products'); // Finished product
            $table->foreignId('warehouse_id')->constrained('warehouses');
            $table->decimal('planned_quantity', 10, 2);
            $table->decimal('actual_quantity', 10, 2)->default(0);
            $table->string('unit', 20);
            $table->enum('status', ['planned', 'released', 'in_progress', 'completed', 'cancelled', 'on_hold'])->default('planned');
            $table->enum('priority', ['low', 'normal', 'high', 'urgent'])->default('normal');
            $table->date('planned_start_date');
            $table->date('planned_end_date');
            $table->date('actual_start_date')->nullable();
            $table->date('actual_end_date')->nullable();
            $table->decimal('planned_material_cost', 15, 2)->default(0);
            $table->decimal('actual_material_cost', 15, 2)->default(0);
            $table->decimal('planned_labor_cost', 15, 2)->default(0);
            $table->decimal('actual_labor_cost', 15, 2)->default(0);
            $table->decimal('planned_overhead_cost', 15, 2)->default(0);
            $table->decimal('actual_overhead_cost', 15, 2)->default(0);
            $table->decimal('total_planned_cost', 15, 2)->default(0);
            $table->decimal('total_actual_cost', 15, 2)->default(0);
            $table->integer('planned_duration_hours')->default(0);
            $table->integer('actual_duration_hours')->default(0);
            $table->decimal('yield_percentage', 5, 2)->default(100); // Actual vs planned output
            $table->foreignId('assigned_to')->nullable()->constrained('users');
            $table->foreignId('created_by')->constrained('users');
            $table->text('notes')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['status', 'planned_start_date']);
            $table->index(['warehouse_id', 'status']);
            $table->index(['product_id', 'status']);
            $table->index(['assigned_to', 'status']);
            $table->index('po_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('production_orders');
    }
};
