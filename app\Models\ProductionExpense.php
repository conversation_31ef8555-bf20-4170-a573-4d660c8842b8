<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProductionExpense extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'expense_number',
        'production_order_id',
        'expense_type',
        'category',
        'description',
        'amount',
        'currency',
        'expense_date',
        'status',
        'priority',
        'requested_by',
        'approved_by',
        'approved_at',
        'paid_by',
        'paid_at',
        'notes',
        'rejection_reason',
        'attachments',
        'vendor_name',
        'invoice_number',
        'due_date',
        'warehouse_id',
    ];

    protected $casts = [
        'expense_date' => 'date',
        'due_date' => 'date',
        'approved_at' => 'datetime',
        'paid_at' => 'datetime',
        'amount' => 'decimal:2',
        'attachments' => 'array',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->expense_number)) {
                $model->expense_number = static::generateExpenseNumber();
            }
        });
    }

    public static function generateExpenseNumber(): string
    {
        $prefix = 'PE';
        $date = now()->format('Ymd');
        $lastNumber = static::whereDate('created_at', today())
            ->where('expense_number', 'like', $prefix . $date . '%')
            ->count() + 1;

        return $prefix . $date . str_pad($lastNumber, 4, '0', STR_PAD_LEFT);
    }

    // Relationships
    public function productionOrder(): BelongsTo
    {
        return $this->belongsTo(ProductionOrder::class);
    }

    public function requestedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'requested_by');
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function paidBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'paid_by');
    }

    public function warehouse(): BelongsTo
    {
        return $this->belongsTo(Warehouse::class);
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('expense_type', $type);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    // Methods
    public function approve($approvedBy = null)
    {
        $this->update([
            'status' => 'approved',
            'approved_by' => $approvedBy ?? auth()->id(),
            'approved_at' => now(),
        ]);
    }

    public function reject($reason = null)
    {
        $this->update([
            'status' => 'rejected',
            'rejection_reason' => $reason,
        ]);
    }

    public function markAsPaid($paidBy = null)
    {
        $this->update([
            'status' => 'paid',
            'paid_by' => $paidBy ?? auth()->id(),
            'paid_at' => now(),
        ]);
    }

    public function getStatusBadgeColorAttribute(): string
    {
        return match ($this->status) {
            'pending' => 'warning',
            'approved' => 'success',
            'rejected' => 'danger',
            'paid' => 'primary',
            default => 'gray',
        };
    }

    public function getPriorityBadgeColorAttribute(): string
    {
        return match ($this->priority) {
            'low' => 'gray',
            'normal' => 'primary',
            'high' => 'warning',
            'urgent' => 'danger',
            default => 'gray',
        };
    }
}
