<?php

namespace App\Filament\Manufacturing\Resources;

use App\Filament\Manufacturing\Resources\EquipmentMaintenanceResource\Pages;
use App\Filament\Manufacturing\Resources\EquipmentMaintenanceResource\RelationManagers;
use App\Models\EquipmentMaintenance;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class EquipmentMaintenanceResource extends Resource
{
    protected static ?string $model = EquipmentMaintenance::class;

    protected static ?string $navigationIcon = 'heroicon-o-wrench-screwdriver';

    protected static ?string $navigationGroup = 'Maintenance & Repair';

    protected static ?string $navigationLabel = 'Maintenance Alat';

    protected static ?string $modelLabel = 'Maintenance Alat';

    protected static ?string $pluralModelLabel = 'Maintenance Alat';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Alat')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('maintenance_number')
                                    ->label('Nomor Maintenance')
                                    ->disabled()
                                    ->dehydrated(false),

                                Forms\Components\TextInput::make('equipment_name')
                                    ->label('Nama Alat')
                                    ->required()
                                    ->maxLength(255),
                            ]),

                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\TextInput::make('equipment_code')
                                    ->label('Kode Alat')
                                    ->maxLength(255),

                                Forms\Components\Select::make('equipment_type')
                                    ->label('Jenis Alat')
                                    ->options([
                                        'Machine' => 'Mesin',
                                        'Tool' => 'Alat',
                                        'Vehicle' => 'Kendaraan',
                                        'Instrument' => 'Instrumen',
                                        'Equipment' => 'Peralatan',
                                        'Other' => 'Lainnya',
                                    ]),

                                Forms\Components\Select::make('warehouse_id')
                                    ->label('Lokasi/Gudang')
                                    ->relationship('warehouse', 'name')
                                    ->searchable(),
                            ]),

                        Forms\Components\Textarea::make('equipment_description')
                            ->label('Deskripsi Alat')
                            ->rows(3)
                            ->columnSpanFull(),
                    ]),

                Forms\Components\Section::make('Detail Maintenance')
                    ->schema([
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\Select::make('maintenance_type')
                                    ->label('Jenis Maintenance')
                                    ->options([
                                        'preventive' => 'Preventif',
                                        'corrective' => 'Korektif',
                                        'emergency' => 'Darurat',
                                        'routine' => 'Rutin',
                                    ])
                                    ->required(),

                                Forms\Components\Select::make('priority')
                                    ->label('Prioritas')
                                    ->options([
                                        'low' => 'Rendah',
                                        'normal' => 'Normal',
                                        'high' => 'Tinggi',
                                        'critical' => 'Kritis',
                                    ])
                                    ->default('normal')
                                    ->required(),

                                Forms\Components\Select::make('status')
                                    ->label('Status')
                                    ->options([
                                        'scheduled' => 'Terjadwal',
                                        'in_progress' => 'Sedang Berlangsung',
                                        'completed' => 'Selesai',
                                        'cancelled' => 'Dibatalkan',
                                        'on_hold' => 'Ditunda',
                                    ])
                                    ->default('scheduled')
                                    ->required(),
                            ]),

                        Forms\Components\Textarea::make('description')
                            ->label('Deskripsi Maintenance')
                            ->required()
                            ->rows(3)
                            ->columnSpanFull(),

                        Forms\Components\Textarea::make('problem_description')
                            ->label('Deskripsi Masalah')
                            ->rows(3)
                            ->columnSpanFull(),
                    ]),

                Forms\Components\Section::make('Jadwal & Durasi')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\DateTimePicker::make('scheduled_start')
                                    ->label('Jadwal Mulai')
                                    ->required(),

                                Forms\Components\DateTimePicker::make('scheduled_end')
                                    ->label('Jadwal Selesai')
                                    ->required(),
                            ]),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\DateTimePicker::make('actual_start')
                                    ->label('Mulai Aktual'),

                                Forms\Components\DateTimePicker::make('actual_end')
                                    ->label('Selesai Aktual'),
                            ]),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('estimated_duration_hours')
                                    ->label('Estimasi Durasi (Jam)')
                                    ->numeric(),

                                Forms\Components\TextInput::make('actual_duration_hours')
                                    ->label('Durasi Aktual (Jam)')
                                    ->numeric()
                                    ->disabled()
                                    ->dehydrated(false),
                            ]),
                    ]),

                Forms\Components\Section::make('Biaya & Personil')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('estimated_cost')
                                    ->label('Estimasi Biaya')
                                    ->numeric()
                                    ->prefix('Rp'),

                                Forms\Components\TextInput::make('actual_cost')
                                    ->label('Biaya Aktual')
                                    ->numeric()
                                    ->prefix('Rp'),
                            ]),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Select::make('assigned_to')
                                    ->label('Ditugaskan Kepada')
                                    ->relationship('assignedTo', 'name')
                                    ->searchable(),

                                Forms\Components\Select::make('supervisor_id')
                                    ->label('Supervisor')
                                    ->relationship('supervisor', 'name')
                                    ->searchable(),
                            ]),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('vendor_name')
                                    ->label('Nama Vendor')
                                    ->maxLength(255),

                                Forms\Components\TextInput::make('vendor_contact')
                                    ->label('Kontak Vendor')
                                    ->maxLength(255),
                            ]),
                    ]),

                Forms\Components\Section::make('Maintenance Berkala')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\DatePicker::make('next_maintenance_date')
                                    ->label('Tanggal Maintenance Berikutnya'),

                                Forms\Components\TextInput::make('maintenance_interval_days')
                                    ->label('Interval Maintenance (Hari)')
                                    ->numeric(),
                            ]),
                    ]),

                Forms\Components\Section::make('Catatan & Solusi')
                    ->schema([
                        Forms\Components\Textarea::make('solution_description')
                            ->label('Deskripsi Solusi')
                            ->rows(3)
                            ->columnSpanFull(),

                        Forms\Components\Textarea::make('notes')
                            ->label('Catatan Tambahan')
                            ->rows(3)
                            ->columnSpanFull(),

                        Forms\Components\Hidden::make('requested_by')
                            ->default(auth()->id()),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('maintenance_number')
                    ->label('Nomor Maintenance')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('equipment_name')
                    ->label('Nama Alat')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('equipment_type')
                    ->label('Jenis Alat')
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'Machine' => 'Mesin',
                        'Tool' => 'Alat',
                        'Vehicle' => 'Kendaraan',
                        'Instrument' => 'Instrumen',
                        'Equipment' => 'Peralatan',
                        'Other' => 'Lainnya',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('maintenance_type')
                    ->label('Jenis Maintenance')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'preventive' => 'success',
                        'corrective' => 'warning',
                        'emergency' => 'danger',
                        'routine' => 'primary',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'preventive' => 'Preventif',
                        'corrective' => 'Korektif',
                        'emergency' => 'Darurat',
                        'routine' => 'Rutin',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('priority')
                    ->label('Prioritas')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'low' => 'gray',
                        'normal' => 'primary',
                        'high' => 'warning',
                        'critical' => 'danger',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'low' => 'Rendah',
                        'normal' => 'Normal',
                        'high' => 'Tinggi',
                        'critical' => 'Kritis',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'scheduled' => 'warning',
                        'in_progress' => 'primary',
                        'completed' => 'success',
                        'cancelled' => 'danger',
                        'on_hold' => 'gray',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'scheduled' => 'Terjadwal',
                        'in_progress' => 'Sedang Berlangsung',
                        'completed' => 'Selesai',
                        'cancelled' => 'Dibatalkan',
                        'on_hold' => 'Ditunda',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('scheduled_start')
                    ->label('Jadwal Mulai')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('assignedTo.name')
                    ->label('Ditugaskan Kepada')
                    ->searchable(),

                Tables\Columns\TextColumn::make('estimated_cost')
                    ->label('Estimasi Biaya')
                    ->money('IDR')
                    ->sortable(),

                Tables\Columns\TextColumn::make('actual_cost')
                    ->label('Biaya Aktual')
                    ->money('IDR')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('next_maintenance_date')
                    ->label('Maintenance Berikutnya')
                    ->date()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('maintenance_type')
                    ->label('Jenis Maintenance')
                    ->options([
                        'preventive' => 'Preventif',
                        'corrective' => 'Korektif',
                        'emergency' => 'Darurat',
                        'routine' => 'Rutin',
                    ]),

                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        'scheduled' => 'Terjadwal',
                        'in_progress' => 'Sedang Berlangsung',
                        'completed' => 'Selesai',
                        'cancelled' => 'Dibatalkan',
                        'on_hold' => 'Ditunda',
                    ]),

                Tables\Filters\SelectFilter::make('priority')
                    ->label('Prioritas')
                    ->options([
                        'low' => 'Rendah',
                        'normal' => 'Normal',
                        'high' => 'Tinggi',
                        'critical' => 'Kritis',
                    ]),

                Tables\Filters\Filter::make('scheduled_start')
                    ->label('Jadwal Mulai')
                    ->form([
                        Forms\Components\DatePicker::make('from')
                            ->label('Dari'),
                        Forms\Components\DatePicker::make('until')
                            ->label('Sampai'),
                    ])
                    ->query(function ($query, array $data) {
                        return $query
                            ->when($data['from'], fn($query, $date) => $query->whereDate('scheduled_start', '>=', $date))
                            ->when($data['until'], fn($query, $date) => $query->whereDate('scheduled_start', '<=', $date));
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('start')
                    ->label('Mulai')
                    ->icon('heroicon-o-play')
                    ->color('success')
                    ->visible(fn($record) => $record->status === 'scheduled')
                    ->action(fn($record) => $record->start())
                    ->requiresConfirmation(),
                Tables\Actions\Action::make('complete')
                    ->label('Selesai')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->visible(fn($record) => $record->status === 'in_progress')
                    ->form([
                        Forms\Components\Textarea::make('solution_description')
                            ->label('Deskripsi Solusi')
                            ->required(),
                        Forms\Components\TextInput::make('actual_cost')
                            ->label('Biaya Aktual')
                            ->numeric()
                            ->prefix('Rp'),
                    ])
                    ->action(function ($record, array $data) {
                        $record->update($data);
                        $record->complete();
                    })
                    ->requiresConfirmation(),
                Tables\Actions\Action::make('hold')
                    ->label('Tunda')
                    ->icon('heroicon-o-pause')
                    ->color('warning')
                    ->visible(fn($record) => in_array($record->status, ['scheduled', 'in_progress']))
                    ->action(fn($record) => $record->hold())
                    ->requiresConfirmation(),
                Tables\Actions\Action::make('cancel')
                    ->label('Batal')
                    ->icon('heroicon-o-x-circle')
                    ->color('danger')
                    ->visible(fn($record) => in_array($record->status, ['scheduled', 'in_progress', 'on_hold']))
                    ->action(fn($record) => $record->cancel())
                    ->requiresConfirmation(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('scheduled_start', 'asc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListEquipmentMaintenances::route('/'),
            'create' => Pages\CreateEquipmentMaintenance::route('/create'),
            'edit' => Pages\EditEquipmentMaintenance::route('/{record}/edit'),
        ];
    }
}
