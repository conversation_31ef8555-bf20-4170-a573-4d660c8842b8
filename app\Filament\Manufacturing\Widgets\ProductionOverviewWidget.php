<?php

namespace App\Filament\Manufacturing\Widgets;

use App\Models\ProductionOrder;
use App\Models\QualityInspection;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class ProductionOverviewWidget extends BaseWidget
{
    protected function getStats(): array
    {
        $totalOrders = ProductionOrder::count();
        $activeOrders = ProductionOrder::whereIn('status', ['released', 'in_progress'])->count();
        $completedToday = ProductionOrder::where('status', 'completed')
            ->whereDate('actual_end_date', today())
            ->count();
        $qualityPassRate = $this->getQualityPassRate();

        return [
            Stat::make('Total Order Produksi', $totalOrders)
                ->description('Semua order produksi')
                ->descriptionIcon('heroicon-m-clipboard-document-list')
                ->color('primary'),

            Stat::make('Order Aktif', $activeOrders)
                ->description('Sedang dalam produksi')
                ->descriptionIcon('heroicon-m-cog-6-tooth')
                ->color('warning'),

            Stat::make('Selesai Hari Ini', $completedToday)
                ->description('Order selesai hari ini')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success'),

            Stat::make('Tingkat Kelulusan QC', number_format($qualityPassRate, 1) . '%')
                ->description('Rata-rata 30 hari terakhir')
                ->descriptionIcon('heroicon-m-shield-check')
                ->color($qualityPassRate >= 95 ? 'success' : ($qualityPassRate >= 90 ? 'warning' : 'danger')),
        ];
    }

    protected function getQualityPassRate(): float
    {
        $totalInspections = QualityInspection::where('created_at', '>=', now()->subDays(30))->count();

        if ($totalInspections === 0) {
            return 0;
        }

        $passedInspections = QualityInspection::where('created_at', '>=', now()->subDays(30))
            ->where('overall_result', 'passed')
            ->count();

        return ($passedInspections / $totalInspections) * 100;
    }
}
