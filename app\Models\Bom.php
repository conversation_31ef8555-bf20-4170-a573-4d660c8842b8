<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Bom extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'bom_number',
        'name',
        'description',
        'product_id',
        'version',
        'status',
        'quantity',
        'unit',
        'total_material_cost',
        'labor_cost_per_unit',
        'overhead_cost_per_unit',
        'total_cost_per_unit',
        'lead_time_days',
        'notes',
        'created_by',
        'approved_by',
        'approved_at',
    ];

    protected $casts = [
        'quantity' => 'decimal:2',
        'total_material_cost' => 'decimal:2',
        'labor_cost_per_unit' => 'decimal:2',
        'overhead_cost_per_unit' => 'decimal:2',
        'total_cost_per_unit' => 'decimal:2',
        'approved_at' => 'datetime',
    ];

    // Relationships
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function bomItems(): HasMany
    {
        return $this->hasMany(BomItem::class);
    }

    public function productionOrders(): HasMany
    {
        return $this->hasMany(ProductionOrder::class);
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeForProduct($query, $productId)
    {
        return $query->where('product_id', $productId);
    }

    // Accessors & Mutators
    public function getIsActiveAttribute(): bool
    {
        return $this->status === 'active';
    }

    public function getIsApprovedAttribute(): bool
    {
        return !is_null($this->approved_at);
    }

    // Methods
    public function calculateTotalCost(): void
    {
        $materialCost = $this->bomItems()->sum('total_cost');
        $this->update([
            'total_material_cost' => $materialCost,
            'total_cost_per_unit' => $materialCost + $this->labor_cost_per_unit + $this->overhead_cost_per_unit,
        ]);
    }

    public function approve(User $user): void
    {
        $this->update([
            'status' => 'active',
            'approved_by' => $user->id,
            'approved_at' => now(),
        ]);
    }

    public function generateBomNumber(): string
    {
        $prefix = 'BOM';
        $date = now()->format('Ymd');
        $sequence = static::whereDate('created_at', today())->count() + 1;
        
        return sprintf('%s%s%04d', $prefix, $date, $sequence);
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($bom) {
            if (empty($bom->bom_number)) {
                $bom->bom_number = $bom->generateBomNumber();
            }
        });
    }
}
