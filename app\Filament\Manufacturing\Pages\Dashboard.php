<?php

namespace App\Filament\Manufacturing\Pages;

use Filament\Pages\Dashboard as BaseDashboard;

class Dashboard extends BaseDashboard
{
    protected static ?string $navigationIcon = 'heroicon-o-home';

    protected static string $view = 'filament.manufacturing.pages.dashboard';

    public function getWidgets(): array
    {
        return [
            \App\Filament\Manufacturing\Widgets\ProductionOverviewWidget::class,
            \App\Filament\Manufacturing\Widgets\ProductionOrdersStatsWidget::class,
            \App\Filament\Manufacturing\Widgets\ProductionEfficiencyWidget::class,
            \App\Filament\Manufacturing\Widgets\QualityControlStatsWidget::class,
            \App\Filament\Manufacturing\Widgets\CostAnalysisWidget::class,
            \App\Filament\Manufacturing\Widgets\RecentProductionOrdersWidget::class,
        ];
    }

    public function getColumns(): int | string | array
    {
        return [
            'md' => 2,
            'xl' => 3,
        ];
    }
}
