<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('production_schedules', function (Blueprint $table) {
            $table->id();
            $table->foreignId('production_plan_id')->constrained('production_plans')->onDelete('cascade');
            $table->foreignId('production_order_id')->constrained('production_orders')->onDelete('cascade');
            $table->date('scheduled_date');
            $table->time('scheduled_start_time');
            $table->time('scheduled_end_time');
            $table->integer('scheduled_duration_minutes');
            $table->decimal('scheduled_quantity', 10, 2);
            $table->foreignId('assigned_operator')->nullable()->constrained('users');
            $table->foreignId('assigned_supervisor')->nullable()->constrained('users');
            $table->enum('shift', ['morning', 'afternoon', 'night'])->default('morning');
            $table->enum('status', ['scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'rescheduled'])->default('scheduled');
            $table->datetime('actual_start_time')->nullable();
            $table->datetime('actual_end_time')->nullable();
            $table->decimal('actual_quantity', 10, 2)->default(0);
            $table->text('notes')->nullable();
            $table->text('delay_reason')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['production_plan_id', 'scheduled_date']);
            $table->index(['production_order_id', 'status']);
            $table->index(['scheduled_date', 'shift']);
            $table->index(['assigned_operator', 'scheduled_date']);
            $table->unique(['production_order_id', 'scheduled_date', 'shift'], 'unique_schedule_slot');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('production_schedules');
    }
};
