<?php

namespace App\Filament\Manufacturing\Resources\ProductionTransactionResource\Pages;

use App\Filament\Manufacturing\Resources\ProductionTransactionResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListProductionTransactions extends ListRecords
{
    protected static string $resource = ProductionTransactionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
