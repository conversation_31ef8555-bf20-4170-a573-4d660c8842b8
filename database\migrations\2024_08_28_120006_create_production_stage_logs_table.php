<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('production_stage_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('production_stage_id')->constrained('production_stages')->onDelete('cascade');
            $table->enum('action', ['started', 'paused', 'resumed', 'completed', 'failed', 'notes_added']);
            $table->text('description')->nullable();
            $table->decimal('quantity_processed', 10, 2)->nullable();
            $table->decimal('quantity_good', 10, 2)->nullable();
            $table->decimal('quantity_defective', 10, 2)->nullable();
            $table->decimal('quantity_waste', 10, 2)->nullable();
            $table->foreignId('logged_by')->constrained('users');
            $table->json('additional_data')->nullable(); // For flexible data storage
            $table->timestamps();

            // Indexes
            $table->index(['production_stage_id', 'created_at']);
            $table->index(['action', 'created_at']);
            $table->index('logged_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('production_stage_logs');
    }
};
