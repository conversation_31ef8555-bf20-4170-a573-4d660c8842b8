<?php

namespace App\Filament\Manufacturing\Resources\QualityControlPointResource\Pages;

use App\Filament\Manufacturing\Resources\QualityControlPointResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewQualityControlPoint extends ViewRecord
{
    protected static string $resource = QualityControlPointResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
