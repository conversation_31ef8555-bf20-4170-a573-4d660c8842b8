<?php

namespace App\Filament\Manufacturing\Resources\MaterialConsumptionResource\Pages;

use App\Filament\Manufacturing\Resources\MaterialConsumptionResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewMaterialConsumption extends ViewRecord
{
    protected static string $resource = MaterialConsumptionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
