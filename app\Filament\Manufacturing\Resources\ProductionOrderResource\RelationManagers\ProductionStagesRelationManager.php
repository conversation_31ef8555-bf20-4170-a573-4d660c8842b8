<?php

namespace App\Filament\Manufacturing\Resources\ProductionOrderResource\RelationManagers;

use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class ProductionStagesRelationManager extends RelationManager
{
    protected static string $relationship = 'productionStages';

    protected static ?string $recordTitleAttribute = 'stage_name';

    protected static ?string $title = 'Production Stages';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\TextInput::make('stage_name')
                            ->label('Stage Name')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\TextInput::make('sequence')
                            ->numeric()
                            ->required()
                            ->default(0),
                    ]),

                Forms\Components\Textarea::make('description')
                    ->rows(2)
                    ->columnSpanFull(),

                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\DateTimePicker::make('planned_start_time')
                            ->required(),

                        Forms\Components\DateTimePicker::make('planned_end_time')
                            ->required(),
                    ]),

                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\TextInput::make('planned_duration_minutes')
                            ->label('Planned Duration (Minutes)')
                            ->numeric()
                            ->required(),

                        Forms\Components\TextInput::make('planned_labor_cost')
                            ->label('Planned Labor Cost')
                            ->numeric()
                            ->prefix('Rp')
                            ->default(0),
                    ]),

                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\Select::make('assigned_to')
                            ->label('Assigned To')
                            ->options(User::all()->pluck('name', 'id'))
                            ->searchable(),

                        Forms\Components\Toggle::make('requires_quality_check')
                            ->label('Requires Quality Check')
                            ->default(false),
                    ]),

                Forms\Components\Textarea::make('instructions')
                    ->label('Work Instructions')
                    ->rows(3)
                    ->columnSpanFull(),

                Forms\Components\Textarea::make('notes')
                    ->rows(2)
                    ->columnSpanFull(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('stage_name')
            ->columns([
                Tables\Columns\TextColumn::make('sequence')
                    ->sortable(),

                Tables\Columns\TextColumn::make('stage_name')
                    ->label('Stage')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'pending' => 'gray',
                        'in_progress' => 'warning',
                        'completed' => 'success',
                        'failed' => 'danger',
                        'skipped' => 'secondary',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('assignedUser.name')
                    ->label('Assigned To')
                    ->searchable(),

                Tables\Columns\TextColumn::make('planned_start_time')
                    ->label('Planned Start')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('planned_end_time')
                    ->label('Planned End')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('actual_start_time')
                    ->label('Actual Start')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('actual_end_time')
                    ->label('Actual End')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('planned_duration_minutes')
                    ->label('Planned Duration')
                    ->suffix(' min'),

                Tables\Columns\TextColumn::make('actual_duration_minutes')
                    ->label('Actual Duration')
                    ->suffix(' min'),

                Tables\Columns\IconColumn::make('requires_quality_check')
                    ->label('QC Required')
                    ->boolean(),

                Tables\Columns\IconColumn::make('quality_check_passed')
                    ->label('QC Passed')
                    ->boolean(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'pending' => 'Pending',
                        'in_progress' => 'In Progress',
                        'completed' => 'Completed',
                        'failed' => 'Failed',
                        'skipped' => 'Skipped',
                    ]),

                Tables\Filters\TernaryFilter::make('requires_quality_check')
                    ->label('Requires Quality Check'),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),

                Tables\Actions\Action::make('start')
                    ->label('Start')
                    ->icon('heroicon-o-play')
                    ->color('success')
                    ->visible(fn ($record) => $record->status === 'pending' && $record->canStart())
                    ->requiresConfirmation()
                    ->action(function ($record) {
                        $record->start(auth()->user());
                    }),

                Tables\Actions\Action::make('complete')
                    ->label('Complete')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->visible(fn ($record) => $record->status === 'in_progress')
                    ->form([
                        Forms\Components\Toggle::make('quality_check_passed')
                            ->label('Quality Check Passed')
                            ->visible(fn ($record) => $record->requires_quality_check),

                        Forms\Components\Textarea::make('completion_notes')
                            ->label('Completion Notes')
                            ->rows(2),
                    ])
                    ->action(function ($record, array $data) {
                        $record->complete(auth()->user(), $data);
                    }),

                Tables\Actions\Action::make('fail')
                    ->label('Mark as Failed')
                    ->icon('heroicon-o-x-circle')
                    ->color('danger')
                    ->visible(fn ($record) => $record->status === 'in_progress')
                    ->form([
                        Forms\Components\Textarea::make('failure_reason')
                            ->label('Failure Reason')
                            ->required()
                            ->rows(3),
                    ])
                    ->action(function ($record, array $data) {
                        $record->fail(auth()->user(), $data['failure_reason']);
                    }),

                Tables\Actions\Action::make('pause')
                    ->label('Pause')
                    ->icon('heroicon-o-pause')
                    ->color('warning')
                    ->visible(fn ($record) => $record->status === 'in_progress')
                    ->form([
                        Forms\Components\Textarea::make('pause_reason')
                            ->label('Pause Reason')
                            ->rows(2),
                    ])
                    ->action(function ($record, array $data) {
                        $record->pause(auth()->user(), $data['pause_reason'] ?? null);
                    }),

                Tables\Actions\Action::make('resume')
                    ->label('Resume')
                    ->icon('heroicon-o-play')
                    ->color('success')
                    ->visible(fn ($record) => $record->status === 'in_progress')
                    ->action(function ($record) {
                        $record->resume(auth()->user());
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('sequence');
    }
}
