<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shop_floor_controls', function (Blueprint $table) {
            $table->id();
            $table->string('control_number')->unique();
            $table->foreignId('production_order_id')->constrained()->onDelete('cascade');
            $table->foreignId('workstation_id')->nullable()->constrained('users')->onDelete('set null'); // User as workstation operator
            $table->string('workstation_name')->nullable();
            $table->enum('status', ['waiting', 'in_progress', 'completed', 'paused', 'stopped'])->default('waiting');
            $table->timestamp('start_time')->nullable();
            $table->timestamp('end_time')->nullable();
            $table->integer('planned_duration_minutes')->nullable();
            $table->integer('actual_duration_minutes')->nullable();
            $table->decimal('planned_quantity', 10, 2)->nullable();
            $table->decimal('actual_quantity', 10, 2)->nullable();
            $table->decimal('good_quantity', 10, 2)->nullable();
            $table->decimal('defect_quantity', 10, 2)->nullable();
            $table->decimal('efficiency_percentage', 5, 2)->nullable();
            $table->text('notes')->nullable();
            $table->json('downtime_logs')->nullable(); // Track downtime reasons and duration
            $table->json('quality_checks')->nullable(); // Real-time quality check results
            $table->foreignId('supervisor_id')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['production_order_id', 'status']);
            $table->index(['workstation_id', 'start_time']);
            $table->index(['status', 'start_time']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shop_floor_controls');
    }
};
