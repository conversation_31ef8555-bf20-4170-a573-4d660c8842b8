<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class EquipmentMaintenance extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'maintenance_number',
        'equipment_name',
        'equipment_code',
        'equipment_type',
        'equipment_description',
        'maintenance_type',
        'priority',
        'status',
        'description',
        'problem_description',
        'solution_description',
        'scheduled_start',
        'scheduled_end',
        'actual_start',
        'actual_end',
        'estimated_duration_hours',
        'actual_duration_hours',
        'estimated_cost',
        'actual_cost',
        'assigned_to',
        'supervisor_id',
        'requested_by',
        'approved_by',
        'approved_at',
        'notes',
        'spare_parts_used',
        'attachments',
        'vendor_name',
        'vendor_contact',
        'next_maintenance_date',
        'maintenance_interval_days',
        'warehouse_id',
    ];

    protected $casts = [
        'scheduled_start' => 'datetime',
        'scheduled_end' => 'datetime',
        'actual_start' => 'datetime',
        'actual_end' => 'datetime',
        'approved_at' => 'datetime',
        'next_maintenance_date' => 'date',
        'estimated_cost' => 'decimal:2',
        'actual_cost' => 'decimal:2',
        'spare_parts_used' => 'array',
        'attachments' => 'array',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->maintenance_number)) {
                $model->maintenance_number = static::generateMaintenanceNumber();
            }
        });
    }

    public static function generateMaintenanceNumber(): string
    {
        $prefix = 'MRO';
        $date = now()->format('Ymd');
        $lastNumber = static::whereDate('created_at', today())
            ->where('maintenance_number', 'like', $prefix . $date . '%')
            ->count() + 1;

        return $prefix . $date . str_pad($lastNumber, 4, '0', STR_PAD_LEFT);
    }

    // Relationships
    public function assignedTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function supervisor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'supervisor_id');
    }

    public function requestedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'requested_by');
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function warehouse(): BelongsTo
    {
        return $this->belongsTo(Warehouse::class);
    }

    // Scopes
    public function scopeScheduled($query)
    {
        return $query->where('status', 'scheduled');
    }

    public function scopeInProgress($query)
    {
        return $query->where('status', 'in_progress');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('maintenance_type', $type);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    // Methods
    public function start()
    {
        $this->update([
            'status' => 'in_progress',
            'actual_start' => now(),
        ]);
    }

    public function complete()
    {
        $endTime = now();
        $actualDuration = $this->actual_start ? $this->actual_start->diffInHours($endTime) : 0;

        $this->update([
            'status' => 'completed',
            'actual_end' => $endTime,
            'actual_duration_hours' => $actualDuration,
        ]);

        $this->scheduleNextMaintenance();
    }

    public function cancel()
    {
        $this->update([
            'status' => 'cancelled',
        ]);
    }

    public function hold()
    {
        $this->update([
            'status' => 'on_hold',
        ]);
    }

    public function approve($approvedBy = null)
    {
        $this->update([
            'approved_by' => $approvedBy ?? auth()->id(),
            'approved_at' => now(),
        ]);
    }

    public function scheduleNextMaintenance()
    {
        if ($this->maintenance_interval_days) {
            $this->update([
                'next_maintenance_date' => now()->addDays($this->maintenance_interval_days),
            ]);
        }
    }

    public function addSparePart($partName, $quantity, $cost = null)
    {
        $spareParts = $this->spare_parts_used ?? [];
        $spareParts[] = [
            'part_name' => $partName,
            'quantity' => $quantity,
            'cost' => $cost,
            'added_at' => now()->toISOString(),
        ];

        $this->update(['spare_parts_used' => $spareParts]);
    }

    public function getStatusBadgeColorAttribute(): string
    {
        return match ($this->status) {
            'scheduled' => 'warning',
            'in_progress' => 'primary',
            'completed' => 'success',
            'cancelled' => 'danger',
            'on_hold' => 'gray',
            default => 'gray',
        };
    }

    public function getPriorityBadgeColorAttribute(): string
    {
        return match ($this->priority) {
            'low' => 'gray',
            'normal' => 'primary',
            'high' => 'warning',
            'critical' => 'danger',
            default => 'gray',
        };
    }
}
