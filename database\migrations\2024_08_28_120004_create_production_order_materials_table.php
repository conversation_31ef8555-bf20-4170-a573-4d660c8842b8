<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('production_order_materials', function (Blueprint $table) {
            $table->id();
            $table->foreignId('production_order_id')->constrained('production_orders')->onDelete('cascade');
            $table->foreignId('product_id')->constrained('products'); // Material/component
            $table->decimal('required_quantity', 10, 4);
            $table->decimal('allocated_quantity', 10, 4)->default(0);
            $table->decimal('consumed_quantity', 10, 4)->default(0);
            $table->string('unit', 20);
            $table->decimal('unit_cost', 15, 2)->default(0);
            $table->decimal('total_cost', 15, 2)->default(0);
            $table->enum('status', ['pending', 'allocated', 'partially_consumed', 'fully_consumed'])->default('pending');
            $table->boolean('is_critical')->default(false);
            $table->date('required_date');
            $table->foreignId('warehouse_id')->constrained('warehouses');
            $table->text('notes')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['production_order_id', 'status']);
            $table->index(['product_id', 'required_date']);
            $table->index(['warehouse_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('production_order_materials');
    }
};
