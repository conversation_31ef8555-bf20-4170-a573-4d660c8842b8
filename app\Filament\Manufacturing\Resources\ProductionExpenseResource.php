<?php

namespace App\Filament\Manufacturing\Resources;

use App\Filament\Manufacturing\Resources\ProductionExpenseResource\Pages;
use App\Filament\Manufacturing\Resources\ProductionExpenseResource\RelationManagers;
use App\Models\ProductionExpense;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ProductionExpenseResource extends Resource
{
    protected static ?string $model = ProductionExpense::class;

    protected static ?string $navigationIcon = 'heroicon-o-banknotes';

    protected static ?string $navigationGroup = 'Operasi Produksi';

    protected static ?string $navigationLabel = 'Permintaan Pengeluaran';

    protected static ?string $modelLabel = 'Permintaan Pengeluaran';

    protected static ?string $pluralModelLabel = 'Permintaan Pengeluaran';

    protected static ?int $navigationSort = 5;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Permintaan')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('expense_number')
                                    ->label('Nomor Permintaan')
                                    ->disabled()
                                    ->dehydrated(false),

                                Forms\Components\Select::make('production_order_id')
                                    ->label('Order Produksi')
                                    ->relationship('productionOrder', 'po_number')
                                    ->searchable()
                                    ->nullable(),
                            ]),

                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\Select::make('expense_type')
                                    ->label('Jenis Pengeluaran')
                                    ->options([
                                        'material' => 'Material',
                                        'labor' => 'Tenaga Kerja',
                                        'overhead' => 'Overhead',
                                        'maintenance' => 'Maintenance',
                                        'utility' => 'Utilitas',
                                        'other' => 'Lainnya',
                                    ])
                                    ->required(),

                                Forms\Components\TextInput::make('category')
                                    ->label('Kategori')
                                    ->placeholder('Sub-kategori pengeluaran'),

                                Forms\Components\Select::make('priority')
                                    ->label('Prioritas')
                                    ->options([
                                        'low' => 'Rendah',
                                        'normal' => 'Normal',
                                        'high' => 'Tinggi',
                                        'urgent' => 'Mendesak',
                                    ])
                                    ->default('normal')
                                    ->required(),
                            ]),
                    ]),

                Forms\Components\Section::make('Detail Pengeluaran')
                    ->schema([
                        Forms\Components\Textarea::make('description')
                            ->label('Deskripsi')
                            ->required()
                            ->rows(3)
                            ->columnSpanFull(),

                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\TextInput::make('amount')
                                    ->label('Jumlah')
                                    ->numeric()
                                    ->prefix('Rp')
                                    ->required(),

                                Forms\Components\Select::make('currency')
                                    ->label('Mata Uang')
                                    ->options([
                                        'IDR' => 'IDR',
                                        'USD' => 'USD',
                                        'EUR' => 'EUR',
                                    ])
                                    ->default('IDR')
                                    ->required(),

                                Forms\Components\DatePicker::make('expense_date')
                                    ->label('Tanggal Pengeluaran')
                                    ->default(today())
                                    ->required(),
                            ]),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('vendor_name')
                                    ->label('Nama Vendor'),

                                Forms\Components\TextInput::make('invoice_number')
                                    ->label('Nomor Invoice'),
                            ]),

                        Forms\Components\DatePicker::make('due_date')
                            ->label('Tanggal Jatuh Tempo'),
                    ]),

                Forms\Components\Section::make('Informasi Tambahan')
                    ->schema([
                        Forms\Components\Select::make('warehouse_id')
                            ->label('Gudang')
                            ->relationship('warehouse', 'name')
                            ->searchable(),

                        Forms\Components\Textarea::make('notes')
                            ->label('Catatan')
                            ->rows(3)
                            ->columnSpanFull(),

                        Forms\Components\Hidden::make('requested_by')
                            ->default(auth()->id()),

                        Forms\Components\Hidden::make('status')
                            ->default('pending'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('expense_number')
                    ->label('Nomor Permintaan')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('productionOrder.po_number')
                    ->label('Order Produksi')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('expense_type')
                    ->label('Jenis')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'material' => 'primary',
                        'labor' => 'success',
                        'overhead' => 'warning',
                        'maintenance' => 'danger',
                        'utility' => 'info',
                        'other' => 'gray',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'material' => 'Material',
                        'labor' => 'Tenaga Kerja',
                        'overhead' => 'Overhead',
                        'maintenance' => 'Maintenance',
                        'utility' => 'Utilitas',
                        'other' => 'Lainnya',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('description')
                    ->label('Deskripsi')
                    ->limit(50)
                    ->searchable(),

                Tables\Columns\TextColumn::make('amount')
                    ->label('Jumlah')
                    ->money('IDR')
                    ->sortable(),

                Tables\Columns\TextColumn::make('priority')
                    ->label('Prioritas')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'low' => 'gray',
                        'normal' => 'primary',
                        'high' => 'warning',
                        'urgent' => 'danger',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'low' => 'Rendah',
                        'normal' => 'Normal',
                        'high' => 'Tinggi',
                        'urgent' => 'Mendesak',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'pending' => 'warning',
                        'approved' => 'success',
                        'rejected' => 'danger',
                        'paid' => 'primary',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'pending' => 'Menunggu',
                        'approved' => 'Disetujui',
                        'rejected' => 'Ditolak',
                        'paid' => 'Dibayar',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('expense_date')
                    ->label('Tanggal')
                    ->date()
                    ->sortable(),

                Tables\Columns\TextColumn::make('requestedBy.name')
                    ->label('Diminta Oleh')
                    ->searchable(),

                Tables\Columns\TextColumn::make('vendor_name')
                    ->label('Vendor')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('expense_type')
                    ->label('Jenis Pengeluaran')
                    ->options([
                        'material' => 'Material',
                        'labor' => 'Tenaga Kerja',
                        'overhead' => 'Overhead',
                        'maintenance' => 'Maintenance',
                        'utility' => 'Utilitas',
                        'other' => 'Lainnya',
                    ]),

                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        'pending' => 'Menunggu',
                        'approved' => 'Disetujui',
                        'rejected' => 'Ditolak',
                        'paid' => 'Dibayar',
                    ]),

                Tables\Filters\SelectFilter::make('priority')
                    ->label('Prioritas')
                    ->options([
                        'low' => 'Rendah',
                        'normal' => 'Normal',
                        'high' => 'Tinggi',
                        'urgent' => 'Mendesak',
                    ]),

                Tables\Filters\Filter::make('expense_date')
                    ->label('Tanggal Pengeluaran')
                    ->form([
                        Forms\Components\DatePicker::make('from')
                            ->label('Dari'),
                        Forms\Components\DatePicker::make('until')
                            ->label('Sampai'),
                    ])
                    ->query(function ($query, array $data) {
                        return $query
                            ->when($data['from'], fn($query, $date) => $query->whereDate('expense_date', '>=', $date))
                            ->when($data['until'], fn($query, $date) => $query->whereDate('expense_date', '<=', $date));
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('approve')
                    ->label('Setujui')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->visible(fn($record) => $record->status === 'pending')
                    ->action(fn($record) => $record->approve())
                    ->requiresConfirmation(),
                Tables\Actions\Action::make('reject')
                    ->label('Tolak')
                    ->icon('heroicon-o-x-circle')
                    ->color('danger')
                    ->visible(fn($record) => $record->status === 'pending')
                    ->form([
                        Forms\Components\Textarea::make('rejection_reason')
                            ->label('Alasan Penolakan')
                            ->required(),
                    ])
                    ->action(function ($record, array $data) {
                        $record->reject($data['rejection_reason']);
                    })
                    ->requiresConfirmation(),
                Tables\Actions\Action::make('mark_paid')
                    ->label('Tandai Dibayar')
                    ->icon('heroicon-o-banknotes')
                    ->color('primary')
                    ->visible(fn($record) => $record->status === 'approved')
                    ->action(fn($record) => $record->markAsPaid())
                    ->requiresConfirmation(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('expense_date', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProductionExpenses::route('/'),
            'create' => Pages\CreateProductionExpense::route('/create'),
            'edit' => Pages\EditProductionExpense::route('/{record}/edit'),
        ];
    }
}
