<?php

namespace App\Filament\Manufacturing\Widgets;

use App\Models\ProductionOrder;
use Filament\Widgets\ChartWidget;

class ProductionEfficiencyWidget extends ChartWidget
{
    protected static ?string $heading = 'Tren Efisiensi Produksi';

    protected static ?int $sort = 2;

    protected function getData(): array
    {
        $last30Days = collect(range(0, 29))->map(function ($day) {
            return now()->subDays($day)->format('Y-m-d');
        })->reverse()->values();

        $efficiencyData = [];
        $yieldData = [];
        $labels = [];

        foreach ($last30Days as $date) {
            $orders = ProductionOrder::whereDate('actual_end_date', $date)
                ->where('status', 'completed')
                ->get();

            if ($orders->isNotEmpty()) {
                // Calculate efficiency (actual vs planned duration)
                $avgEfficiency = $orders->avg(function ($order) {
                    if ($order->planned_duration_hours > 0) {
                        return ($order->planned_duration_hours / max($order->actual_duration_hours, 1)) * 100;
                    }
                    return 100;
                });

                // Calculate yield (actual vs planned quantity)
                $avgYield = $orders->avg(function ($order) {
                    if ($order->planned_quantity > 0) {
                        return ($order->actual_quantity / $order->planned_quantity) * 100;
                    }
                    return 100;
                });

                $efficiencyData[] = round($avgEfficiency, 1);
                $yieldData[] = round($avgYield, 1);
            } else {
                $efficiencyData[] = null;
                $yieldData[] = null;
            }

            $labels[] = now()->createFromFormat('Y-m-d', $date)->format('M j');
        }

        return [
            'datasets' => [
                [
                    'label' => 'Time Efficiency (%)',
                    'data' => $efficiencyData,
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'borderColor' => 'rgb(59, 130, 246)',
                    'borderWidth' => 2,
                    'fill' => true,
                    'tension' => 0.4,
                ],
                [
                    'label' => 'Yield Rate (%)',
                    'data' => $yieldData,
                    'backgroundColor' => 'rgba(16, 185, 129, 0.1)',
                    'borderColor' => 'rgb(16, 185, 129)',
                    'borderWidth' => 2,
                    'fill' => true,
                    'tension' => 0.4,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
                'tooltip' => [
                    'mode' => 'index',
                    'intersect' => false,
                ],
            ],
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'max' => 120,
                    'ticks' => [
                        'callback' => 'function(value) { return value + "%"; }',
                    ],
                ],
                'x' => [
                    'display' => true,
                ],
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
            'interaction' => [
                'mode' => 'nearest',
                'axis' => 'x',
                'intersect' => false,
            ],
        ];
    }
}
