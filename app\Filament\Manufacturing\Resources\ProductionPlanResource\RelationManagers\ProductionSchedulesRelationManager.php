<?php

namespace App\Filament\Manufacturing\Resources\ProductionPlanResource\RelationManagers;

use App\Models\ProductionOrder;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class ProductionSchedulesRelationManager extends RelationManager
{
    protected static string $relationship = 'productionSchedules';

    protected static ?string $recordTitleAttribute = 'scheduled_date';

    protected static ?string $title = 'Production Schedules';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\Select::make('production_order_id')
                            ->label('Production Order')
                            ->options(ProductionOrder::where('production_plan_id', $this->getOwnerRecord()->id)
                                ->with('product')
                                ->get()
                                ->mapWithKeys(function ($po) {
                                    return [$po->id => $po->po_number . ' - ' . $po->product->name];
                                }))
                            ->searchable()
                            ->required(),

                        Forms\Components\Select::make('shift')
                            ->options([
                                'morning' => 'Morning (08:00-16:00)',
                                'afternoon' => 'Afternoon (16:00-00:00)',
                                'night' => 'Night (00:00-08:00)',
                            ])
                            ->default('morning')
                            ->required(),
                    ]),

                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\DatePicker::make('scheduled_date')
                            ->required(),

                        Forms\Components\TextInput::make('scheduled_quantity')
                            ->label('Scheduled Quantity')
                            ->numeric()
                            ->required(),
                    ]),

                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\TimePicker::make('scheduled_start_time')
                            ->required(),

                        Forms\Components\TimePicker::make('scheduled_end_time')
                            ->required(),
                    ]),

                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\Select::make('assigned_operator')
                            ->label('Assigned Operator')
                            ->options(User::all()->pluck('name', 'id'))
                            ->searchable(),

                        Forms\Components\Select::make('assigned_supervisor')
                            ->label('Assigned Supervisor')
                            ->options(User::all()->pluck('name', 'id'))
                            ->searchable(),
                    ]),

                Forms\Components\Textarea::make('notes')
                    ->rows(2)
                    ->columnSpanFull(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('scheduled_date')
            ->columns([
                Tables\Columns\TextColumn::make('productionOrder.po_number')
                    ->label('Production Order')
                    ->searchable(),

                Tables\Columns\TextColumn::make('productionOrder.product.name')
                    ->label('Product')
                    ->searchable()
                    ->wrap(),

                Tables\Columns\TextColumn::make('scheduled_date')
                    ->label('Date')
                    ->date()
                    ->sortable(),

                Tables\Columns\TextColumn::make('shift')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'morning' => 'success',
                        'afternoon' => 'warning',
                        'night' => 'info',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('scheduled_start_time')
                    ->label('Start Time')
                    ->time(),

                Tables\Columns\TextColumn::make('scheduled_end_time')
                    ->label('End Time')
                    ->time(),

                Tables\Columns\TextColumn::make('scheduled_quantity')
                    ->label('Scheduled Qty')
                    ->numeric(),

                Tables\Columns\TextColumn::make('actual_quantity')
                    ->label('Actual Qty')
                    ->numeric(),

                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'scheduled' => 'gray',
                        'confirmed' => 'info',
                        'in_progress' => 'warning',
                        'completed' => 'success',
                        'cancelled' => 'danger',
                        'rescheduled' => 'secondary',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('operator.name')
                    ->label('Operator')
                    ->searchable(),

                Tables\Columns\TextColumn::make('supervisor.name')
                    ->label('Supervisor')
                    ->searchable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'scheduled' => 'Scheduled',
                        'confirmed' => 'Confirmed',
                        'in_progress' => 'In Progress',
                        'completed' => 'Completed',
                        'cancelled' => 'Cancelled',
                        'rescheduled' => 'Rescheduled',
                    ]),

                Tables\Filters\SelectFilter::make('shift')
                    ->options([
                        'morning' => 'Morning',
                        'afternoon' => 'Afternoon',
                        'night' => 'Night',
                    ]),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->mutateFormDataUsing(function (array $data): array {
                        // Auto-calculate duration
                        if (isset($data['scheduled_start_time']) && isset($data['scheduled_end_time'])) {
                            $start = \Carbon\Carbon::parse($data['scheduled_start_time']);
                            $end = \Carbon\Carbon::parse($data['scheduled_end_time']);
                            $data['scheduled_duration_minutes'] = $end->diffInMinutes($start);
                        }
                        return $data;
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),

                Tables\Actions\Action::make('confirm')
                    ->label('Confirm')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->visible(fn ($record) => $record->status === 'scheduled')
                    ->action(function ($record) {
                        $record->update(['status' => 'confirmed']);
                    }),

                Tables\Actions\Action::make('start')
                    ->label('Start')
                    ->icon('heroicon-o-play')
                    ->color('warning')
                    ->visible(fn ($record) => $record->status === 'confirmed')
                    ->action(function ($record) {
                        $record->start();
                    }),

                Tables\Actions\Action::make('complete')
                    ->label('Complete')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->visible(fn ($record) => $record->status === 'in_progress')
                    ->form([
                        Forms\Components\TextInput::make('actual_quantity')
                            ->label('Actual Quantity Produced')
                            ->numeric()
                            ->required(),

                        Forms\Components\Textarea::make('completion_notes')
                            ->label('Completion Notes')
                            ->rows(2),
                    ])
                    ->action(function ($record, array $data) {
                        $record->complete($data['actual_quantity'], $data['completion_notes'] ?? null);
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('scheduled_date', 'desc');
    }
}
