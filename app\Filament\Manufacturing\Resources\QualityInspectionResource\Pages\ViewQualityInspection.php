<?php

namespace App\Filament\Manufacturing\Resources\QualityInspectionResource\Pages;

use App\Filament\Manufacturing\Resources\QualityInspectionResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewQualityInspection extends ViewRecord
{
    protected static string $resource = QualityInspectionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
            
            Actions\Action::make('approve')
                ->label('Approve Inspection')
                ->icon('heroicon-o-check-circle')
                ->color('success')
                ->requiresConfirmation()
                ->visible(fn () => $this->record->overall_result === 'pending')
                ->action(function () {
                    $this->record->update(['overall_result' => 'passed']);
                    $this->refreshFormData(['overall_result']);
                }),

            Actions\Action::make('reject')
                ->label('Reject Inspection')
                ->icon('heroicon-o-x-circle')
                ->color('danger')
                ->requiresConfirmation()
                ->visible(fn () => $this->record->overall_result === 'pending')
                ->action(function () {
                    $this->record->update([
                        'overall_result' => 'failed',
                        'requires_rework' => true,
                    ]);
                    $this->refreshFormData(['overall_result', 'requires_rework']);
                }),

            Actions\Action::make('recalculate')
                ->label('Recalculate Result')
                ->icon('heroicon-o-calculator')
                ->color('info')
                ->action(function () {
                    $this->record->calculateOverallResult();
                    $this->refreshFormData(['overall_result', 'quantity_passed', 'quantity_failed', 'quantity_rework']);
                }),
        ];
    }
}
