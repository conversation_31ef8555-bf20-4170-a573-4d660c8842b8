<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('production_expenses', function (Blueprint $table) {
            $table->id();
            $table->string('expense_number')->unique();
            $table->foreignId('production_order_id')->nullable()->constrained()->onDelete('cascade');
            $table->enum('expense_type', ['material', 'labor', 'overhead', 'maintenance', 'utility', 'other']);
            $table->string('category')->nullable(); // Sub-category for expense type
            $table->string('description');
            $table->decimal('amount', 12, 2);
            $table->string('currency', 3)->default('IDR');
            $table->date('expense_date');
            $table->enum('status', ['pending', 'approved', 'rejected', 'paid'])->default('pending');
            $table->enum('priority', ['low', 'normal', 'high', 'urgent'])->default('normal');
            $table->foreignId('requested_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('approved_at')->nullable();
            $table->foreignId('paid_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('paid_at')->nullable();
            $table->text('notes')->nullable();
            $table->text('rejection_reason')->nullable();
            $table->json('attachments')->nullable(); // File attachments
            $table->string('vendor_name')->nullable();
            $table->string('invoice_number')->nullable();
            $table->date('due_date')->nullable();
            $table->foreignId('warehouse_id')->nullable()->constrained()->onDelete('set null');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['production_order_id', 'expense_date']);
            $table->index(['expense_type', 'status']);
            $table->index(['status', 'expense_date']);
            $table->index(['requested_by', 'expense_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('production_expenses');
    }
};
