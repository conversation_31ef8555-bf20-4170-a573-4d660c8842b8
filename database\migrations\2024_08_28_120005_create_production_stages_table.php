<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('production_stages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('production_order_id')->constrained('production_orders')->onDelete('cascade');
            $table->string('stage_name');
            $table->text('description')->nullable();
            $table->integer('sequence')->default(0);
            $table->enum('status', ['pending', 'in_progress', 'completed', 'failed', 'skipped'])->default('pending');
            $table->datetime('planned_start_time');
            $table->datetime('planned_end_time');
            $table->datetime('actual_start_time')->nullable();
            $table->datetime('actual_end_time')->nullable();
            $table->integer('planned_duration_minutes');
            $table->integer('actual_duration_minutes')->default(0);
            $table->decimal('planned_labor_cost', 15, 2)->default(0);
            $table->decimal('actual_labor_cost', 15, 2)->default(0);
            $table->foreignId('assigned_to')->nullable()->constrained('users');
            $table->boolean('requires_quality_check')->default(false);
            $table->boolean('quality_check_passed')->nullable();
            $table->text('instructions')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['production_order_id', 'sequence']);
            $table->index(['status', 'planned_start_time']);
            $table->index(['assigned_to', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('production_stages');
    }
};
