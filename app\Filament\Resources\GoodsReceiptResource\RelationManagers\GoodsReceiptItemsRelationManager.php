<?php

namespace App\Filament\Resources\GoodsReceiptResource\RelationManagers;

use App\Models\PurchaseOrderItem;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class GoodsReceiptItemsRelationManager extends RelationManager
{
    protected static string $relationship = 'goodsReceiptItems';

    protected static ?string $title = 'Items Goods Receipt';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('purchase_order_item_id')
                    ->label('Item PO')
                    ->options(function () {
                        $goodsReceipt = $this->getOwnerRecord();
                        if (!$goodsReceipt->purchase_order_id) {
                            return [];
                        }

                        return PurchaseOrderItem::where('purchase_order_id', $goodsReceipt->purchase_order_id)
                            ->whereColumn('quantity_received', '<', 'quantity_ordered')
                            ->with('product')
                            ->get()
                            ->mapWithKeys(function ($item) {
                                return [$item->id => $item->product->name . ' (Sisa: ' . $item->remaining_quantity . ')'];
                            });
                    })
                    ->required()
                    ->searchable()
                    ->preload()
                    ->reactive()
                    ->afterStateUpdated(function ($state, callable $set) {
                        if ($state) {
                            $poItem = PurchaseOrderItem::with('product')->find($state);
                            if ($poItem) {
                                $set('product_id', $poItem->product_id);
                                $set('unit_cost', $poItem->unit_price);
                                $set('quantity_received', min($poItem->remaining_quantity, 1));
                            }
                        }
                    }),
                Forms\Components\TextInput::make('product_id')
                    ->label('Product ID')
                    ->hidden()
                    ->dehydrated(),
                Forms\Components\TextInput::make('quantity_received')
                    ->label('Jumlah Diterima')
                    ->numeric()
                    ->required()
                    ->minValue(1)
                    ->reactive()
                    ->afterStateUpdated(function ($state, callable $get, callable $set) {
                        $unitCost = $get('unit_cost') ?? 0;
                        $set('total_cost', $state * $unitCost);
                    }),
                Forms\Components\TextInput::make('unit_cost')
                    ->label('Harga Satuan')
                    ->numeric()
                    ->required()
                    ->prefix('Rp')
                    ->reactive()
                    ->afterStateUpdated(function ($state, callable $get, callable $set) {
                        $quantity = $get('quantity_received') ?? 0;
                        $set('total_cost', $quantity * $state);
                    }),
                Forms\Components\TextInput::make('total_cost')
                    ->label('Total Biaya')
                    ->numeric()
                    ->prefix('Rp')
                    ->disabled()
                    ->dehydrated(false),
                Forms\Components\Textarea::make('notes')
                    ->label('Catatan')
                    ->rows(2)
                    ->columnSpanFull(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('product.name')
            ->columns([
                Tables\Columns\TextColumn::make('product.sku')
                    ->label('Kode Produk')
                    ->searchable(),
                Tables\Columns\TextColumn::make('product.name')
                    ->label('Nama Produk')
                    ->searchable()
                    ->wrap(),
                Tables\Columns\TextColumn::make('purchaseOrderItem.quantity_ordered')
                    ->label('Qty Dipesan')
                    ->alignCenter(),
                Tables\Columns\TextColumn::make('quantity_received')
                    ->label('Qty Diterima')
                    ->alignCenter(),
                Tables\Columns\TextColumn::make('unit_cost')
                    ->label('Harga Satuan')
                    ->money('IDR'),
                Tables\Columns\TextColumn::make('total_cost')
                    ->label('Total')
                    ->money('IDR'),
                Tables\Columns\TextColumn::make('variance_quantity')
                    ->label('Selisih Qty')
                    ->alignCenter()
                    ->color(fn($record) => $record->hasQuantityVariance() ? 'warning' : 'success'),
                Tables\Columns\TextColumn::make('variance_cost')
                    ->label('Selisih Harga')
                    ->money('IDR')
                    ->color(fn($record) => $record->hasCostVariance() ? 'warning' : 'success'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->visible(fn() => $this->getOwnerRecord()->isEditable())
                    ->mutateFormDataUsing(function (array $data): array {
                        // Ensure product_id is set from purchase_order_item
                        if (isset($data['purchase_order_item_id'])) {
                            $poItem = \App\Models\PurchaseOrderItem::find($data['purchase_order_item_id']);
                            if ($poItem) {
                                $data['product_id'] = $poItem->product_id;
                            }
                        }
                        return $data;
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->visible(fn() => $this->getOwnerRecord()->isEditable()),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn() => $this->getOwnerRecord()->isEditable()),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(fn() => $this->getOwnerRecord()->isEditable()),
                ]),
            ]);
    }
}
