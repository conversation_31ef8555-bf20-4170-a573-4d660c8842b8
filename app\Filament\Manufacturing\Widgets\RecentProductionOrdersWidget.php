<?php

namespace App\Filament\Manufacturing\Widgets;

use App\Models\ProductionOrder;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;

class RecentProductionOrdersWidget extends BaseWidget
{
    protected static ?string $heading = 'Order Produksi Terbaru';

    protected int | string | array $columnSpan = 'full';

    public function table(Table $table): Table
    {
        return $table
            ->query(
                ProductionOrder::query()
                    ->with(['product', 'bom', 'assignedUser'])
                    ->latest()
                    ->limit(10)
            )
            ->columns([
                Tables\Columns\TextColumn::make('po_number')
                    ->label('PO Number')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('product.name')
                    ->label('Product')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('planned_quantity')
                    ->label('Planned Qty')
                    ->numeric()
                    ->sortable(),

                Tables\Columns\TextColumn::make('actual_quantity')
                    ->label('Actual Qty')
                    ->numeric()
                    ->sortable(),

                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'planned' => 'gray',
                        'released' => 'info',
                        'in_progress' => 'warning',
                        'completed' => 'success',
                        'cancelled' => 'danger',
                        'on_hold' => 'secondary',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('assignedUser.name')
                    ->label('Assigned To')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('planned_start_date')
                    ->label('Start Date')
                    ->date()
                    ->sortable(),

                Tables\Columns\TextColumn::make('planned_end_date')
                    ->label('End Date')
                    ->date()
                    ->sortable(),
            ])
            ->actions([
                Tables\Actions\Action::make('view')
                    ->icon('heroicon-m-eye')
                    ->url(fn(ProductionOrder $record): string => route('filament.manufacturing.resources.production-orders.view', $record)),
            ])
            ->paginated(false);
    }
}
