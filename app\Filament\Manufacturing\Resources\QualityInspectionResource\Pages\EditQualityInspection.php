<?php

namespace App\Filament\Manufacturing\Resources\QualityInspectionResource\Pages;

use App\Filament\Manufacturing\Resources\QualityInspectionResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditQualityInspection extends EditRecord
{
    protected static string $resource = QualityInspectionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', ['record' => $this->getRecord()]);
    }

    protected function afterSave(): void
    {
        // Auto-calculate overall result after save
        $this->record->calculateOverallResult();
    }
}
