<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProductionTransaction extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'transaction_number',
        'production_order_id',
        'product_id',
        'transaction_type',
        'quantity',
        'unit',
        'unit_cost',
        'total_cost',
        'notes',
        'transaction_date',
        'recorded_by',
        'approved_by',
        'approved_at',
        'status',
        'metadata',
    ];

    protected $casts = [
        'transaction_date' => 'datetime',
        'approved_at' => 'datetime',
        'quantity' => 'decimal:2',
        'unit_cost' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'metadata' => 'array',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->transaction_number)) {
                $model->transaction_number = static::generateTransactionNumber();
            }
        });
    }

    public static function generateTransactionNumber(): string
    {
        $prefix = 'PT';
        $date = now()->format('Ymd');
        $lastNumber = static::whereDate('created_at', today())
            ->where('transaction_number', 'like', $prefix . $date . '%')
            ->count() + 1;

        return $prefix . $date . str_pad($lastNumber, 4, '0', STR_PAD_LEFT);
    }

    // Relationships
    public function productionOrder(): BelongsTo
    {
        return $this->belongsTo(ProductionOrder::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function recordedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'recorded_by');
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    // Scopes
    public function scopeByType($query, $type)
    {
        return $query->where('transaction_type', $type);
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    // Methods
    public function approve($approvedBy = null)
    {
        $this->update([
            'status' => 'approved',
            'approved_by' => $approvedBy ?? auth()->id(),
            'approved_at' => now(),
        ]);
    }

    public function reject()
    {
        $this->update([
            'status' => 'rejected',
        ]);
    }

    public function calculateTotalCost()
    {
        if ($this->unit_cost && $this->quantity) {
            $this->total_cost = $this->unit_cost * $this->quantity;
            $this->save();
        }
    }
}
